import { useMutation, UseMutationOptions, useQuery, useQueryClient, UseQueryOptions } from "@tanstack/react-query";
import {
    GetForwardingConfigRequest,
    GetForwardingConfigResponse,
    UpdateForwardingConfigRequest,
    UpdateForwardingConfigResponse,
} from "proto/hero/orgs/v1/orgs_pb";
import { getForwardingConfig, updateForwardingConfig } from "./endpoints";
import {
    AddToContactBookRequest,
    AddToContactBookResponse,
    ContactRecord,
    DeleteFromContactBookRequest,
    DeleteFromContactBookResponse,
    GetContactByPhoneNumberRequest,
    GetContactByPhoneNumberResponse,
    GetContactFromContactBookRequest,
    GetContactFromContactBookResponse,
    ListContactsInContactBookRequest,
    UpdateContactInContactBookRequest,
    UpdateContactInContactBookResponse,
} from "proto/hero/orgs/v1/orgs_pb";

import {
    addToContactBook,
    deleteFromContactBook,
    getContactByPhoneNumber,
    getContactFromContactBook,
    listContactsInContactBook,
    updateContactInContactBook,
} from "./endpoints";

// Cache key constants
const ORGS_QUERY_KEY = "orgs" as const;
const ORG_CONTACTS_QUERY_KEY = "orgContacts";
const ORG_CONTACT_QUERY_KEY = "orgContact";


/**
 * Hook for getting the forwarding configuration for an organization.
 * @param orgId - The ID of the organization to get the forwarding configuration for.
 * @param options - Optional query options.
 * @returns The forwarding configuration for the organization.
 */
export const forwardingConfigKey = (orgId: number) =>
  [ORGS_QUERY_KEY, "forwardingConfig", orgId] as const;

export function useGetForwardingConfig(
  orgId: number | undefined,
  options?: Omit<UseQueryOptions<GetForwardingConfigResponse, Error, GetForwardingConfigResponse>, "queryKey" | "queryFn">
) {
  const key = orgId ? forwardingConfigKey(orgId) : ([ORGS_QUERY_KEY, "forwardingConfig", "disabled"] as const);

  return useQuery<GetForwardingConfigResponse, Error>({
    queryKey: key,
    queryFn: () => {
      if (!orgId) throw new Error("orgId required");
      return getForwardingConfig({ orgId } as GetForwardingConfigRequest);
    },
    enabled: !!orgId,
    staleTime: 5 * 1000,
    retry: 2,
    ...options,
  });
}

export function useUpdateForwardingConfig(
  options?: UseMutationOptions<UpdateForwardingConfigResponse, Error, UpdateForwardingConfigRequest>
) {
  const qc = useQueryClient();
  return useMutation<UpdateForwardingConfigResponse, Error, UpdateForwardingConfigRequest>({
    mutationFn: updateForwardingConfig,
    onSuccess: (data, vars) => {
      const orgId = data.config?.orgId ?? vars.orgId;
      if (orgId) {
        qc.setQueryData(forwardingConfigKey(orgId), data);
        qc.invalidateQueries({ queryKey: forwardingConfigKey(orgId) });
      }
    },
    ...options,
  });
}

/**
 * Hook for loading all contacts with progressive loading and search support.
 * This hook automatically loads additional pages as needed and provides search capabilities
 * across all contacts in the organization's contact book.
 */
export function useAllContactsInContactBook(
    orgId: number,
    searchQuery?: string,
    options?: Omit<UseQueryOptions<{ contacts: ContactRecord[], totalCount: number }, Error>, 'queryKey' | 'queryFn'>
) {
    return useQuery({
        queryKey: [ORG_CONTACTS_QUERY_KEY, 'all', orgId, searchQuery],
        queryFn: async () => {
            let allContacts: ContactRecord[] = [];
            let nextPageToken = "";
            let totalCount = 0;
            const pageSize = 100; // Use maximum page size
            
            // Load all contacts using pagination
            do {
                const response = await listContactsInContactBook({
                    orgId,
                    pageToken: nextPageToken,
                    pageSize
                } as ListContactsInContactBookRequest);
                
                if (response.contacts) {
                    allContacts = [...allContacts, ...response.contacts];
                }
                
                totalCount = response.totalCount || 0;
                nextPageToken = response.nextPageToken || "";
                
                // Safety check to prevent infinite loops
                if (allContacts.length >= totalCount) {
                    break;
                }
            } while (nextPageToken);
            
            return {
                contacts: allContacts,
                totalCount
            };
        },
        enabled: !!orgId,
        staleTime: 10 * 60 * 1000, // 10 minutes (longer since we're loading all contacts)
        retry: 2,
        ...options,
    });
}

/**
 * Hook for getting a contact by phone number from the organization's contact book.
 */
export function useGetContactByPhoneNumber(
    orgId: number,
    phone: string,
    options?: Omit<UseQueryOptions<GetContactByPhoneNumberResponse, Error>, 'queryKey' | 'queryFn'>
) {
    return useQuery({
        queryKey: [ORG_CONTACT_QUERY_KEY, "phone", orgId, phone],
        queryFn: () => getContactByPhoneNumber({ orgId, phone } as GetContactByPhoneNumberRequest),
        enabled: !!orgId && !!phone,
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: 2,
        ...options,
    });
}

/**
 * Hook for getting a specific contact from the organization's contact book by ID.
 */
export function useGetContactFromContactBook(
    contactId: string,
    options?: Omit<UseQueryOptions<GetContactFromContactBookResponse, Error>, 'queryKey' | 'queryFn'>
) {
    return useQuery({
        queryKey: [ORG_CONTACT_QUERY_KEY, contactId],
        queryFn: () => getContactFromContactBook({ id: contactId } as GetContactFromContactBookRequest),
        enabled: !!contactId,
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: 2,
        ...options,
    });
}

/**
 * Hook for adding a new contact to the organization's contact book.
 * On success, invalidates the contacts list for the organization.
 */
export function useAddToContactBook(
    options?: UseMutationOptions<AddToContactBookResponse, Error, AddToContactBookRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (request: AddToContactBookRequest) => addToContactBook(request),
        onSuccess: (data) => {
            if (data.contact?.orgId) {
                queryClient.invalidateQueries({ queryKey: [ORG_CONTACTS_QUERY_KEY, data.contact.orgId] });
            }
        },
        ...options,
    });
}

/**
 * Hook for updating an existing contact in the organization's contact book.
 * On success, invalidates both the contacts list and the specific contact cache.
 */
export function useUpdateContactInContactBook(
    options?: UseMutationOptions<UpdateContactInContactBookResponse, Error, UpdateContactInContactBookRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (request: UpdateContactInContactBookRequest) => updateContactInContactBook(request),
        onSuccess: (data) => {
            if (data.contact?.orgId) {
                queryClient.invalidateQueries({ queryKey: [ORG_CONTACTS_QUERY_KEY, data.contact.orgId] });
            }
            if (data.contact?.id) {
                queryClient.invalidateQueries({ queryKey: [ORG_CONTACT_QUERY_KEY, data.contact.id] });
            }
        },
        ...options,
    });
}

/**
 * Hook for deleting a contact from the organization's contact book.
 * On success, invalidates the contacts list.
 */
export function useDeleteFromContactBook(
    orgId: number,
    options?: UseMutationOptions<DeleteFromContactBookResponse, Error, DeleteFromContactBookRequest>
) {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (request: DeleteFromContactBookRequest) => deleteFromContactBook(request),
        onSuccess: () => {
            // Use the orgId parameter since DeleteFromContactBookResponse is empty
            queryClient.invalidateQueries({ queryKey: [ORG_CONTACTS_QUERY_KEY, orgId] });
        },
        ...options,
    });
}