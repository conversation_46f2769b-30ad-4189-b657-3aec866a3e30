import { useGetForwardingConfig, useUpdateForwardingConfig } from "@/app/apis/services/orgs/hooks";
import { useListAssets } from "@/app/apis/services/workflow/assets/hooks";
import { useUserAsset } from "@/app/contexts/User/UserAssetContext";
import { formatPhoneNumberForDisplay, standardizeUSPhoneNumber } from "@/app/utils/caller-identification";
import { colors } from "@/design-system/tokens/colors";
import { radius } from "@/design-system/tokens/radius";
import { spacing } from "@/design-system/tokens/spacing";
import { typography } from "@/design-system/tokens/typography";
import Check from '@mui/icons-material/Check';
import InfoIcon from '@mui/icons-material/Info';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { Box, Skeleton, Switch, Tooltip, Typography } from "@mui/material";
import { ListAssetsRequest } from "proto/hero/assets/v2/assets_pb";
import { UpdateForwardingConfigRequest } from "proto/hero/orgs/v1/orgs_pb";
import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useForwardingToast } from "./ForwardingToastProvider";
import { ForwardingConfirmationDialog } from "./ForwardingConfirmationDialog";

interface CallForwardingPanelProps {}

// =============================================
// Component: CallForwardingPanel
// Purpose: Inline panel for Call Forwarding Settings (replaces tabs content)
// =============================================
export const CallForwardingPanel = memo(({}: CallForwardingPanelProps) => {
  const [isMobileDispatchEnabled, setIsMobileDispatchEnabled] = useState(false);
  const [selectedAssetId, setSelectedAssetId] = useState<string>('');
  const [isOverrideEnabled, setIsOverrideEnabled] = useState(false);
  const [selectedOverrideNumber, setSelectedOverrideNumber] = useState<string>('');
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [showDisableDialog, setShowDisableDialog] = useState(false);
  const [pendingConfigUpdate, setPendingConfigUpdate] = useState<{
    phoneNumber: string;
    assetName: string;
    displayNumber: string;
  } | null>(null);
  
  // Mutation state from hook
  const { mutateAsync: updateConfig, isPending: isUpdatingConfig } = useUpdateForwardingConfig();
  const { showToast } = useForwardingToast();

  // Get current user's organization
  const { asset: currentUserAsset } = useUserAsset();

  // Load existing forwarding config with simple polling (~3s)
  const { data: cfgData } = useGetForwardingConfig(currentUserAsset?.orgId, {
    refetchInterval: 3000,
    refetchIntervalInBackground: true,
  });

  // Fetch all assets in the organization for the dispatcher selector
  const { data: assetsResponse, isLoading: isAssetsLoading, isError: isAssetsError, error: assetsError } = useListAssets(
    {
      pageSize: 100, // Get all assets
      pageToken: "",
    } as ListAssetsRequest,
    {
      enabled: !!currentUserAsset?.orgId,
    }
  );

  const assets = assetsResponse?.assets || [];
  // Prefill state from server config once (when both config and assets available)
  const isInitializedFromServerRef = useRef(false);
  useEffect(() => {
    if (isInitializedFromServerRef.current) return;
    const cfg = cfgData?.config;
    if (!cfg || assets.length === 0) return;

    const initialToggle = !!cfg.isCallForwardingEnabled;
    const initialAssigned = cfg.assignedAssetId || '';
    const assignedAsset = assets.find(a => a.id === initialAssigned);
    const serverNumber = cfg.forwardToNumber || '';
    const assignedNumber = assignedAsset?.contactNo || '';

    setIsMobileDispatchEnabled(initialToggle);
    setSelectedAssetId(initialAssigned);

    if (serverNumber) {
      // If the server number differs from assigned asset's number, treat as override
      if (assignedNumber && standardizeUSPhoneNumber(assignedNumber) === standardizeUSPhoneNumber(serverNumber)) {
        setIsOverrideEnabled(false);
        setSelectedOverrideNumber('');
      } else {
        setIsOverrideEnabled(true);
        setSelectedOverrideNumber(serverNumber);
      }
    } else {
      setIsOverrideEnabled(false);
      setSelectedOverrideNumber('');
    }

    isInitializedFromServerRef.current = true;
  }, [cfgData, assets]);

  // Build override options (dedupe by E.164 and exclude selected dispatcher's number)
  const normalizedToRaw = new Map<string, string>();
  for (const asset of assets) {
    const raw = asset.contactNo?.trim();
    if (!raw) continue;
    try {
      const normalized = standardizeUSPhoneNumber(raw);
      if (!normalizedToRaw.has(normalized)) normalizedToRaw.set(normalized, raw);
    } catch (_) {
      // skip invalid
    }
  }
  const uniquePhoneNumbers = Array.from(normalizedToRaw.entries()).map(([normalized, raw]) => ({ normalized, raw }));
  let selectedAssetNormalized = '';
  const selectedAsset = assets.find(a => a.id === selectedAssetId);
  if (selectedAsset?.contactNo) {
    try { selectedAssetNormalized = standardizeUSPhoneNumber(selectedAsset.contactNo); } catch (_) { /* noop */ }
  }
  const overrideOptions = uniquePhoneNumbers.filter(o => o.normalized !== selectedAssetNormalized);

  // Validation logic for Confirm button
  const isConfirmEnabled = useMemo(() => {
    if (!isMobileDispatchEnabled || !selectedAssetId) return false;
    
    if (isOverrideEnabled) {
      // When override is enabled, we just need an override number selected
      return !!selectedOverrideNumber;
    } else {
      // When override is disabled, we need the selected asset to have a phone number
      return !!selectedAsset?.contactNo;
    }
  }, [isMobileDispatchEnabled, selectedAssetId, selectedAsset?.contactNo, isOverrideEnabled, selectedOverrideNumber]);

  const handleToggleChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const next = event.target.checked;
    
    // If turning OFF and currently enabled, show disable confirmation
    if (!next && isMobileDispatchEnabled) {
      const currentConfig = cfgData?.config;
      const isCurrentlyActive = !!currentConfig?.isCallForwardingEnabled;
      
      if (isCurrentlyActive) {
        setShowDisableDialog(true);
        return; // Don't change state yet, wait for confirmation
      }
    }
    
    setIsMobileDispatchEnabled(next);
    console.log('[CallForwardingPanel] Mobile dispatch toggled:', next);
  }, [isMobileDispatchEnabled, cfgData]);

  const handleAssetChange = useCallback((event: React.ChangeEvent<HTMLSelectElement>) => {
    const assetId = event.target.value;
    setSelectedAssetId(assetId);
    const selected = assets.find(asset => asset.id === assetId);
    console.log('[CallForwardingPanel] Asset changed:', selected?.name, selected?.contactNo);
  }, [assets]);

  const handleOverrideChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const next = event.target.checked;
    setIsOverrideEnabled(next);
    console.log('[CallForwardingPanel] Override toggled:', next);
  }, []);

  const handleOverrideNumberChange = useCallback((event: React.ChangeEvent<HTMLSelectElement>) => {
    const phoneNumber = event.target.value;
    setSelectedOverrideNumber(phoneNumber);
    console.log('[CallForwardingPanel] Override number changed:', phoneNumber);
  }, []);

  // Allow clicking the header label area to toggle switch (matches app convention)
  const handleToggleLabelActivate = useCallback(() => {
    const next = !isMobileDispatchEnabled;
    setIsMobileDispatchEnabled(next);
  }, [isMobileDispatchEnabled]);

  // Clear dependent state when mobile dispatch is disabled
  useEffect(() => {
    if (!isMobileDispatchEnabled) {
      setIsOverrideEnabled(false);
      setSelectedOverrideNumber('');
    }
  }, [isMobileDispatchEnabled]);

  // Clear override number when override is turned off
  useEffect(() => {
    if (!isOverrideEnabled) {
      setSelectedOverrideNumber('');
    }
  }, [isOverrideEnabled]);

  const handleConfirmClick = useCallback(async () => {
    if (!isConfirmEnabled || !currentUserAsset?.orgId || isUpdatingConfig) return;
    
    // Determine the phone number and asset info for the new config
    let phoneNumber = '';
    if (isOverrideEnabled && selectedOverrideNumber) {
      phoneNumber = selectedOverrideNumber;
    } else {
      const selectedAsset = assets.find(asset => asset.id === selectedAssetId);
      phoneNumber = selectedAsset?.contactNo || '';
    }
    
    const assetName = assets.find(a => a.id === selectedAssetId)?.name || '—';
    const displayNumber = phoneNumber ? formatPhoneNumberForDisplay(phoneNumber) : '(---) --- ----';
    
    // Check if module is currently active and if config is changing
    const currentConfig = cfgData?.config;
    const isCurrentlyActive = !!currentConfig?.isCallForwardingEnabled;
    
    if (isCurrentlyActive && isMobileDispatchEnabled) {
      // Check if the new config differs from current config
      const currentPhoneNumber = currentConfig.forwardToNumber || '';
      const currentAssetId = currentConfig.assignedAssetId || '';
      
      const configHasChanged = 
        phoneNumber !== currentPhoneNumber || 
        selectedAssetId !== currentAssetId;
      
      if (configHasChanged) {
        // Store pending config and show confirmation dialog
        setPendingConfigUpdate({
          phoneNumber,
          assetName,
          displayNumber,
        });
        setShowConfirmDialog(true);
        return;
      }
    }
    
    // If no confirmation needed, proceed with update
    await performConfigUpdate(phoneNumber, assetName, displayNumber);
  }, [
    isConfirmEnabled, 
    currentUserAsset?.orgId, 
    isUpdatingConfig, 
    isMobileDispatchEnabled, 
    selectedAssetId, 
    isOverrideEnabled, 
    selectedOverrideNumber, 
    assets,
    cfgData
  ]);

  const performConfigUpdate = useCallback(async (phoneNumber: string, assetName: string, displayNumber: string) => {
    if (!currentUserAsset?.orgId) return;
    
    try {
      console.log('[CallForwardingPanel] Updating forwarding config:', {
        orgId: currentUserAsset.orgId,
        isCallForwardingEnabled: isMobileDispatchEnabled,
        forwardToNumber: phoneNumber,
        assignedAssetId: selectedAssetId
      });
      
      const request: UpdateForwardingConfigRequest = {
        $typeName: 'hero.orgs.v1.UpdateForwardingConfigRequest',
        orgId: currentUserAsset.orgId,
        isCallForwardingEnabled: isMobileDispatchEnabled,
        forwardToNumber: phoneNumber,
        assignedAssetId: selectedAssetId
      };
      
      const response = await updateConfig(request);

      console.log('[CallForwardingPanel] Forwarding config updated successfully (hook):', response.config);

      // Build toast message for this user (actor-only)
      const cfgEnabled = !!response.config?.isCallForwardingEnabled;

      if (!cfgEnabled) {
        showToast({ title: 'Call Forwarding Disabled', subtitle: 'Mobile Dispatch Disabled', variant: 'success' });
      } else if (isOverrideEnabled && selectedOverrideNumber) {
        showToast({ title: `Dispatch Forwarded to ${assetName} • ${displayNumber}`, subtitle: 'Default Number Overridden' });
      } else {
        showToast({ title: `Dispatch Forwarded to ${assetName} • ${displayNumber}`, subtitle: 'Mobile Dispatch Enabled' });
      }
      
    } catch (error) {
      console.error('[CallForwardingPanel] Failed to update forwarding config:', error);
      // TODO: Show error message to user
    }
  }, [
    currentUserAsset?.orgId,
    isMobileDispatchEnabled,
    selectedAssetId,
    isOverrideEnabled,
    selectedOverrideNumber,
    updateConfig,
    showToast
  ]);

  const handleDisableCallForwarding = useCallback(async () => {
    if (!currentUserAsset?.orgId) return;
    
    try {
      console.log('[CallForwardingPanel] Disabling call forwarding');
      
      const request: UpdateForwardingConfigRequest = {
        $typeName: 'hero.orgs.v1.UpdateForwardingConfigRequest',
        orgId: currentUserAsset.orgId,
        isCallForwardingEnabled: false,
        forwardToNumber: '',
        assignedAssetId: ''
      };
      
      const response = await updateConfig(request);

      console.log('[CallForwardingPanel] Call forwarding disabled successfully:', response.config);

      // Update UI state
      setIsMobileDispatchEnabled(false);
      
      // Show success toast
      showToast({ 
        title: 'Call Forwarding Disabled', 
        subtitle: 'Mobile Dispatch Disabled', 
        variant: 'success' 
      });
      
    } catch (error) {
      console.error('[CallForwardingPanel] Failed to disable call forwarding:', error);
      // TODO: Show error message to user
    }
  }, [currentUserAsset?.orgId, updateConfig, showToast]);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        width: '100%',
        backgroundColor: '#FFF',
        overflow: 'visible',
        position: 'relative', // Make this a positioned container for the dialog
      }}
    >
      {/* Scrollable Content Area */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          flex: 1,
          overflow: 'visible',
        }}
      >
        {/* Content with Padding */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: spacing.xs,
            padding: spacing.m,
          }}
        >
        {/* Settings Header Section */}
        <Box
          sx={{
            position: 'relative',
            paddingBottom: spacing.s,
          }}
        >
          <Typography
            sx={{
              ...typography.styles.body3,
              color: colors.grey[700],
              fontFeatureSettings: "'liga' off, 'clig' off",
            }}
          >
            Settings
          </Typography>
          {/* Separator line */}
          <Box
            sx={{
              position: 'absolute',
              bottom: 0,
              left: `-${spacing.m}`,
              right: `-${spacing.m}`,
              height: '1px',
              backgroundColor: colors.grey[200],
            }}
          />
        </Box>

        {/* Mobile Dispatch Toggle Section */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: spacing.s,
          }}
        >
          {/* Toggle Control Row */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              width: '100%',
            }}
          >
            {/* Label with Info Icon (clickable box per app convention) */}
            <Box
              onClick={handleToggleLabelActivate}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleToggleLabelActivate();
                }
              }}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: spacing.xs,
                cursor: 'pointer',
              }}
            >
              <Typography component="p"
                sx={{
                  m: 0,
                  ...typography.styles.body4,
                  color: colors.grey[700],
                  fontFeatureSettings: "'liga' off, 'clig' off",
                }}
              >
                Enable mobile dispatch
              </Typography>
              <Tooltip
                title="Forward all calls to a designated phone number"
                placement="bottom"
                slotProps={{
                  tooltip: {
                    sx: {
                      backgroundColor: colors.grey[900],
                      color: colors.grey[50],
                      fontSize: '12px',
                      padding: `${spacing.xs} ${spacing.s}`,
                      borderRadius: radius.s,
                      boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.15)',
                    }
                  },
                  arrow: {
                    sx: {
                      color: colors.grey[900],
                    }
                  }
                }}
                arrow
              >
                <InfoIcon
                  sx={{
                    color: colors.grey[400],
                    fontSize: 14,
                    mt: '2px',
                    cursor: 'pointer',
                  }}
                />
              </Tooltip>
            </Box>

            {/* Switch Control */}
            <Switch
              checked={isMobileDispatchEnabled}
              onChange={handleToggleChange}
              sx={{
                '& .MuiSwitch-switchBase.Mui-checked': {
                  color: colors.grey[50],
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.08)',
                  },
                },
                '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                  backgroundColor: colors.blue[600],
                  opacity: 1,
                },
                '& .MuiSwitch-track': {
                  backgroundColor: colors.grey[300],
                  opacity: 1,
                },
                '& .MuiSwitch-thumb': {
                  color: colors.grey[50],
                },
              }}
            />
          </Box>

          {/* Helper Text */}
          {isMobileDispatchEnabled && (
            <Typography component="p"
              sx={{
                m: 0,
                ...typography.styles.body4,
                color: colors.grey[600],
                fontSize: '12px',
                lineHeight: 1.35,
              }}
            >
              This will forward all calls to a designated phone number. To save this setting, please select from the list below and save.
            </Typography>
          )}
        </Box>

        {/* Dispatcher Configuration Section - only show when toggle is ON */}
        {isMobileDispatchEnabled && (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: spacing.s,
              marginTop: spacing.xxs
            }}
          >
            {/* Dispatcher Selection */}
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: spacing.s,
            }}
          >
            <Typography component="p"
              sx={{
                m: 0,
                ...typography.styles.body4,
                color: colors.grey[700],
                fontFeatureSettings: "'liga' off, 'clig' off",
              }}
            >
              Assigned Dispatcher
            </Typography>

            {/* Loading state */}
            {isAssetsLoading ? (
              <Skeleton variant="rounded" height={40} />
            ) : (
              <Box sx={dropdownContainerSx}>
                <Box
                  component="select"
                  value={selectedAssetId}
                  onChange={handleAssetChange}
                  disabled={!isMobileDispatchEnabled || assets.length === 0 || isAssetsError}
                  sx={{
                    ...baseDropdownSx,
                    ...typography.styles[selectedAssetId ? 'body1' : 'body2'],
                    color: selectedAssetId ? colors.grey[900] : colors.grey[400],
                    fontSize: '13px',
                  }}
                >
                  <option value="">Assign a Dispatcher</option>
                  {assets.map((asset) => {
                    const displayPhone = asset.contactNo
                      ? formatPhoneNumberForDisplay(asset.contactNo)
                      : '(---) --- ----';
                    return (
                      <option key={asset.id} value={asset.id}>
                        {asset.name} • {displayPhone}
                      </option>
                    );
                  })}
                </Box>
                <KeyboardArrowDownIcon sx={dropdownIconSx} />
              </Box>
            )}

            {/* Empty/Error hints */}
            {!isAssetsLoading && assets.length === 0 && (
              <Typography component="p" sx={{ m: 0, ...typography.styles.body4, color: colors.grey[600] }}>
                No dispatchers found in your organization.
              </Typography>
            )}
            {isAssetsError && (
              <Typography component="p" sx={{ m: 0, ...typography.styles.body4, color: colors.grey[600] }} aria-live="polite">
                Failed to load dispatchers{assetsError ? `: ${String(assetsError)}` : ''}
              </Typography>
            )}
          </Box>

          {/* Override Number Section */}
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: spacing.s,
              marginTop: spacing.xxs,
            }}
          >
            {/* Override Checkbox */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: spacing.xs,
                cursor: isMobileDispatchEnabled ? 'pointer' : 'default',
              }}
            >
              <Box
                component="input"
                type="checkbox"
                checked={isOverrideEnabled}
                onChange={handleOverrideChange}
                disabled={!isMobileDispatchEnabled || !selectedAssetId}
                sx={{
                  width: '16px',
                  height: '16px',
                  cursor: (isMobileDispatchEnabled && selectedAssetId) ? 'pointer' : 'not-allowed',
                  accentColor: colors.blue[600],
                }}
              />
              <Typography component="p"
                sx={{
                  m: 0,
                  ...typography.styles.body4,
                  color: colors.grey[700],
                  fontFeatureSettings: "'liga' off, 'clig' off",
                }}
              >
                Override Default Number
              </Typography>
            </Box>

            {/* Override Phone Number Dropdown - only show when checkbox is checked */}
            {isOverrideEnabled && (
              <Box sx={dropdownContainerSx}>
                <Box
                  component="select"
                  value={selectedOverrideNumber}
                  onChange={handleOverrideNumberChange}
                  disabled={overrideOptions.length === 0}
                  sx={{
                    ...baseDropdownSx,
                    ...typography.styles[selectedOverrideNumber ? 'body1' : 'body2'],
                    color: selectedOverrideNumber ? colors.grey[900] : colors.grey[400],
                    fontSize: '13px',
                  }}
                >
                  <option value="">Select a number</option>
                  {overrideOptions.map(({ normalized, raw }) => {
                    const displayPhone = formatPhoneNumberForDisplay(raw);
                    return (
                      <option key={normalized} value={raw}>
                        {displayPhone}
                      </option>
                    );
                  })}
                </Box>
                <KeyboardArrowDownIcon sx={dropdownIconSx} />
              </Box>
            )}
            {!isMobileDispatchEnabled && (
              <Typography component="p" sx={{ m: 0, ...typography.styles.body4, color: colors.grey[600] }}>
                Enable mobile dispatch to configure override number.
              </Typography>
            )}
          </Box>
        </Box>
        )}

        {/* Confirm Button - absolute, anchored to padded wrapper */}
        {isMobileDispatchEnabled && (
          <Box
            sx={{
              position: 'absolute',
              bottom: spacing.s,     // 14px
              left: spacing.m,       // match content padding
              right: spacing.m,
              zIndex: 1,
            }}
          >
            <Box
              onClick={handleConfirmClick}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleConfirmClick();
                }
              }}
              sx={{
                ...baseDropdownSx,
                paddingRight: spacing.s, // no arrow padding for a button
                paddingLeft: spacing.s,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: spacing.xs,
                backgroundColor: colors.blue[100],
                border: `1px solid ${colors.blue[100]}`,
                color: colors.blue[600],
                cursor: (isConfirmEnabled && !isUpdatingConfig) ? 'pointer' : 'not-allowed',
                opacity: (isConfirmEnabled && !isUpdatingConfig) ? 1 : 0.6,
                '&:hover': (isConfirmEnabled && !isUpdatingConfig) ? {
                  backgroundColor: colors.blue[600],
                  color: '#FFF',
                } : {},
              }}
            >
              <Check sx={{ fontSize: 16 }} />
              <Typography sx={{ ...typography.styles.body1 }}>
                {isUpdatingConfig ? 'Saving...' : 'Confirm'}
              </Typography>
            </Box>
          </Box>
        )}
        </Box>
      </Box>

      {/* Update Configuration Confirmation Dialog */}
      <ForwardingConfirmationDialog
        open={showConfirmDialog}
        title="Update Call Forwarding Settings?"
        subtitle="Calls will now be forwarded to"
        targetInfo={pendingConfigUpdate ? `${pendingConfigUpdate.assetName} • ${pendingConfigUpdate.displayNumber}` : ''}
        onConfirm={async () => {
          setShowConfirmDialog(false);
          if (pendingConfigUpdate) {
            await performConfigUpdate(
              pendingConfigUpdate.phoneNumber,
              pendingConfigUpdate.assetName,
              pendingConfigUpdate.displayNumber
            );
            setPendingConfigUpdate(null);
          }
        }}
        onCancel={() => {
          setShowConfirmDialog(false);
          setPendingConfigUpdate(null);
        }}
      />

      {/* Disable Mobile Dispatch Confirmation Dialog */}
      <ForwardingConfirmationDialog
        open={showDisableDialog}
        title="Disable Mobile Dispatch?"
        subtitle="Calls will no longer be forwarded to"
        targetInfo={(() => {
          const currentConfig = cfgData?.config;
          if (currentConfig?.assignedAssetId) {
            const currentAsset = assets.find(a => a.id === currentConfig.assignedAssetId);
            const currentPhoneNumber = currentConfig.forwardToNumber || currentAsset?.contactNo || '';
            const assetName = currentAsset?.name || '—';
            const displayNumber = currentPhoneNumber ? formatPhoneNumberForDisplay(currentPhoneNumber) : '(---) --- ----';
            return `${assetName} • ${displayNumber}`;
          }
          return '—';
        })()}
        onConfirm={async () => {
          setShowDisableDialog(false);
          await handleDisableCallForwarding();
        }}
        onCancel={() => {
          setShowDisableDialog(false);
        }}
      />
    </Box>
  );
});

CallForwardingPanel.displayName = 'CallForwardingPanel';

// =============================================
// Styling Constants
// =============================================

// Dropdown container styling using design system tokens
const dropdownContainerSx = {
  position: 'relative',
  width: '100%',
} as const;

// Base dropdown styling using MUI sx prop
const baseDropdownSx = {
  height: '40px',
  minHeight: '40px',
  padding: `${spacing.xs} ${spacing.s}`,
  paddingRight: `calc(${spacing.s} + 24px + ${spacing.s})`, // Space for icon
  borderRadius: radius.m,
  border: `1px solid ${colors.grey[200]}`,
  backgroundColor: '#FFF',
  fontFeatureSettings: "'liga' off, 'clig' off",
  cursor: 'pointer',
  width: '100%',
  appearance: 'none',
  '&:focus': {
    outline: 'none',
    borderColor: colors.blue[600],
  },
} as const;

// Dropdown arrow icon styling
const dropdownIconSx = {
  position: 'absolute',
  right: spacing.s,
  top: '50%',
  transform: 'translateY(-50%)',
  pointerEvents: 'none',
  color: colors.grey[700],
  fontSize: '20px',
} as const;