import { colors } from "@/design-system/tokens/colors";
import { radius } from "@/design-system/tokens/radius";
import { spacing } from "@/design-system/tokens/spacing";
import { typography } from "@/design-system/tokens/typography";

export const SHARED_STYLES = {
  // Layout styles
  layout: {
    base: {
      display: "flex",
      flexDirection: "column",
      width: "100%",
      minWidth: 0,
      height: "100%",
    },
    container: {
      display: "flex",
      flexDirection: "column",
      height: "100%",
      width: "100%",
      flex: 1,
      alignItems: "stretch",
      boxShadow: "0px 0px 20px 0px rgba(0, 0, 0, 0.10)",
    }
  },
  
  // Header styles
  header: {
    container: {
      display: "flex",
      height: "48px",
      alignItems: "center",
      justifyContent: "space-between",
      px: spacing.m,
      backgroundColor: colors.grey[50],
      borderBottom: `1px solid ${colors.grey[200]}`,
      position: "relative",
      zIndex: 2,
      width: "100%"
    },
    text: {
      ...typography.styles.caps2,
      fontSize: "14px",
      color: colors.grey[900],
      fontFamily: typography.fontFamily.roboto,
    }
  },

  // Icon styles
  icon: {
    container: {
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      cursor: "pointer",
      position: "relative",
      zIndex: 3,
      "&:hover": {
        backgroundColor: colors.grey[100],
      },
      "&:active": {
        backgroundColor: colors.grey[200],
      }
    },
    style: {
      color: colors.grey[700],
      width: 20,
      height: 20
    }
  },

  // Tab styles
  tabs: {
    container: {
      flex: "0 0 auto",
      position: 'relative',
      '&::after': {
        content: '""',
        position: 'absolute',
        bottom: '1px',
        left: 0,
        right: 0,
        height: '1px',
        backgroundColor: colors.grey[200],
      }
    },
    indicator: {
      backgroundColor: colors.blue[600]
    },
    flexContainer: {
      display: 'flex',
      width: '100%'
    },
    tab: {
      display: 'flex',
      minWidth: '36px',
      minHeight: '32px',
      padding: `${spacing.s} ${spacing.l}`,
      justifyContent: 'center',
      alignItems: 'center',
      gap: spacing.s,
      flex: '1 0 0',
      fontFamily: typography.fontFamily.roboto,
      fontSize: '14px',
      textTransform: 'none',
      fontFeatureSettings: "'liga' off, 'clig' off",
      textEdge: 'cap',
      leadingTrim: 'both',
      textAlign: 'center',
      color: colors.grey[500],
      fontWeight: 400,
      lineHeight: '143%',
      letterSpacing: '0.17px',
      '&.Mui-selected': {
        color: colors.grey[600],
        fontWeight: 500,
        lineHeight: '157%',
        letterSpacing: '0.1px'
      },
      "&:hover": {
        backgroundColor: colors.grey[50]
      }
    }
  },

  // Badge styles
  badge: {
    container: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      minWidth: '20px',
      height: '20px',
      padding: `0 ${spacing.xs}`,
      borderRadius: radius.l
    },
    text: {
      ...typography.styles.tag1,
      textAlign: 'center',
      leadingTrim: 'both',
      textEdge: 'cap',
      fontFeatureSettings: "'liga' off, 'clig' off",
      lineHeight: '143%'
    }
  }
} as const; 