"use client";

import { useGetForwardingConfig } from "@/app/apis/services/orgs/hooks";
import { useListAssets } from "@/app/apis/services/workflow/assets/hooks";
import { useUserAsset } from "@/app/contexts/User/UserAssetContext";
import { formatPhoneNumberForDisplay, standardizeUSPhoneNumber } from "@/app/utils/caller-identification";
import { ListAssetsRequest } from "proto/hero/assets/v2/assets_pb";
import { useEffect, useMemo, useRef } from "react";
import { useForwardingToast } from "./ForwardingToastProvider";

/**
 * This component is primarily used to set up polling for ForwardingToastProvider. 
 * This component is used to notify the user when the forwarding configuration changes,
 */
export const ForwardingChangeNotifier: React.FC = () => {
  const { asset: currentUserAsset } = useUserAsset();
  const orgId = currentUserAsset?.orgId;
  const { showToast } = useForwardingToast();

  // Poll forwarding config every ~3s
  const { data: cfgData } = useGetForwardingConfig(orgId, {
    refetchInterval: 3000,
    refetchIntervalInBackground: true,
  });

  // Fetch assets for name/number lookup
  const { data: assetsResponse } = useListAssets(
    { pageSize: 100, pageToken: "" } as ListAssetsRequest,
    { enabled: !!orgId }
  );
  const assets = assetsResponse?.assets || [];

  const computeHash = useMemo(() => {
    const cfg = cfgData?.config;
    if (!cfg) return "";
    const enabled = !!cfg.isCallForwardingEnabled;
    const assigned = cfg.assignedAssetId || "";
    let normalized = cfg.forwardToNumber || "";
    try { if (normalized) normalized = standardizeUSPhoneNumber(normalized); } catch (_) {}
    return `${enabled}|${assigned}|${normalized}`;
  }, [cfgData]);

  const prevHashRef = useRef<string>("");
  const isFirstLoadRef = useRef<boolean>(true);

  useEffect(() => {
    const cfg = cfgData?.config;
    if (!cfg) return;

    // Skip on first load to avoid a toast at initial render
    if (isFirstLoadRef.current) {
      isFirstLoadRef.current = false;
      prevHashRef.current = computeHash;
      return;
    }

    if (computeHash && computeHash !== prevHashRef.current) {
      prevHashRef.current = computeHash;

      const enabled = !!cfg.isCallForwardingEnabled;
      const assignedAsset = assets.find(a => a.id === (cfg.assignedAssetId || ""));
      const assetName = assignedAsset?.name || "—";

      const rawNumber = cfg.forwardToNumber || "";
      const displayNumber = rawNumber ? formatPhoneNumberForDisplay(rawNumber) : "(---) --- ----";

      // Determine override by comparing to assigned asset's contactNo when present
      let isOverride = false;
      try {
        const assigned = assignedAsset?.contactNo ? standardizeUSPhoneNumber(assignedAsset.contactNo) : "";
        const normalized = rawNumber ? standardizeUSPhoneNumber(rawNumber) : "";
        isOverride = !!rawNumber && !!assigned && normalized !== assigned;
      } catch (_) {
        isOverride = !!rawNumber;
      }

      if (!enabled) {
        showToast({ title: "Call Forwarding Disabled", subtitle: "Mobile Dispatch Disabled", variant: 'success' });
      } else if (isOverride) {
        showToast({ title: `Dispatch Forwarded to ${assetName} • ${displayNumber}`, subtitle: "Default Number Overridden" });
      } else {
        showToast({ title: `Dispatch Forwarded to ${assetName} • ${displayNumber}`, subtitle: "Mobile Dispatch Enabled" });
      }
    }
  }, [computeHash, cfgData, assets, showToast]);

  return null;
};

export default ForwardingChangeNotifier;


