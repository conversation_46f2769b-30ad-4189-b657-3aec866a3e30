import { colors } from "@/design-system/tokens/colors";
import { spacing } from "@/design-system/tokens/spacing";
import { typography } from "@/design-system/tokens/typography";
import { Box, Typography } from "@mui/material";
import { memo } from "react";

interface ForwardingConfirmationDialogProps {
  open: boolean;
  title: string;
  subtitle: string;
  targetInfo: string; // "Asset Name • (xxx) xxx-xxxx"
  onConfirm: () => void;
  onCancel: () => void;
}

export const ForwardingConfirmationDialog = memo(({
  open,
  title,
  subtitle,
  targetInfo,
  onConfirm,
  onCancel,
}: ForwardingConfirmationDialogProps) => {
  if (!open) return null;

  return (
    <Box
      sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        width: '100%',
        backgroundColor: 'rgba(255, 255, 255, 0.85)',
        backdropFilter: 'blur(10px)',
        zIndex: 1000,
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
        padding: spacing.m,
      }}
    >
        {/* Title */}
        <Typography
          sx={{
            color: colors.grey[800],
            textAlign: 'center',
            fontFeatureSettings: "'liga' off, 'clig' off",
            fontFamily: typography.fontFamily.roboto,
            fontSize: '18px',
            fontStyle: 'normal',
            fontWeight: 700,
            lineHeight: '140%', // 25.2px
            letterSpacing: '0.17px',
            marginBottom: spacing.xl, // Spacing between title and subtitle
          }}
        >
          {title}
        </Typography>

        {/* Subtitle */}
        <Typography
          sx={{
            ...typography.styles.body2,
            color: colors.grey[600],
            textAlign: 'center',
            marginBottom: spacing.s, // Spacing between subtitle and target info
          }}
        >
          {subtitle}
        </Typography>

        {/* Target Info (Asset Name • Phone Number) */}
        <Typography
          sx={{
            ...typography.styles.h4,
            color: colors.grey[900],
            textAlign: 'center',
            marginBottom: '55px', // 55px spacing between name and buttons
          }}
        >
          {targetInfo}
        </Typography>

        {/* Buttons */}
        <Box
          sx={{
            display: 'flex',
            gap: spacing.xs, // 8px spacing between buttons
            width: '100%',
            paddingLeft: spacing.m, // M padding on sides
            paddingRight: spacing.m,
          }}
        >
          {/* Yes Button */}
          <Box
            onClick={onConfirm}
            sx={{
              flex: 1,
              height: '40px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: '8px',
              backgroundColor: colors.rose[100],
              color: colors.rose[600],
              cursor: 'pointer',
              transition: 'background-color 0.2s, color 0.2s',
              '&:hover': {
                backgroundColor: colors.rose[600],
                color: '#FFF',
              },
            }}
          >
            <Typography
              sx={{
                ...typography.styles.body1,
                fontWeight: 500,
              }}
            >
              Yes
            </Typography>
          </Box>

          {/* No Button */}
          <Box
            onClick={onCancel}
            sx={{
              flex: 1,
              height: '40px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: '8px',
              backgroundColor: colors.grey[200],
              color: colors.grey[700],
              cursor: 'pointer',
              transition: 'background-color 0.2s, color 0.2s',
              '&:hover': {
                backgroundColor: colors.grey[300],
                color: colors.grey[800],
              },
            }}
          >
            <Typography
              sx={{
                ...typography.styles.body1,
                fontWeight: 500,
              }}
            >
              No
            </Typography>
          </Box>
        </Box>
    </Box>
  );
});

ForwardingConfirmationDialog.displayName = 'ForwardingConfirmationDialog';