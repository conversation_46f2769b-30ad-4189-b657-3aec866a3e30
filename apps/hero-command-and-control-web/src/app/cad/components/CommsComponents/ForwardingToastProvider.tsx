"use client";

import { colors } from "@/design-system/tokens/colors";
import { radius } from "@/design-system/tokens/radius";
import { typography } from "@/design-system/tokens/typography";
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import WarningIcon from '@mui/icons-material/Warning';
import { Box, Snackbar, Typography } from "@mui/material";
import { createContext, useCallback, useContext, useMemo, useRef, useState } from "react";

type ForwardingToastPayload = {
  title: string;
  subtitle?: string;
  variant?: 'warning' | 'success';
};

type ForwardingToastContextType = {
  showToast: (payload: ForwardingToastPayload) => void;
};

const ForwardingToastContext = createContext<ForwardingToastContextType | undefined>(undefined);

export const useForwardingToast = (): ForwardingToastContextType => {
  const ctx = useContext(ForwardingToastContext);
  if (!ctx) throw new Error("useForwardingToast must be used within ForwardingToastProvider");
  return ctx;
};

export const ForwardingToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [open, setOpen] = useState(false);
  const [content, setContent] = useState<ForwardingToastPayload | null>(null);

  // Simple dedupe window to avoid accidental double toasts within a second
  const lastHashRef = useRef<string | null>(null);
  const lastTimeRef = useRef<number>(0);

  const showToast = useCallback((payload: ForwardingToastPayload) => {
    const hash = `${payload.title}|${payload.subtitle ?? ''}`;
    const now = Date.now();
    if (lastHashRef.current === hash && now - lastTimeRef.current < 800) return;
    lastHashRef.current = hash;
    lastTimeRef.current = now;
    setContent(payload);
    setOpen(true);
  }, []);

  const value = useMemo(() => ({ showToast }), [showToast]);

  return (
    <ForwardingToastContext.Provider value={value}>
      {children}
      <Snackbar
        open={open}
        autoHideDuration={3000}
        onClose={() => setOpen(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Box
          sx={{
            width: '450px',
            backgroundColor: '#FFFFFF',
            color: colors.grey[900],
            borderRadius: radius.l,
            boxShadow: '0px 8px 24px rgba(0,0,0,0.24)',
            display: 'flex',
            flexDirection: 'row',
          }}
        >
          {/* Left accent bar occupies entire left edge */}
          <Box sx={{
            width: '8px',
            backgroundColor: content?.variant === 'success' ? '#31A24C' : '#F1A817',
            flexShrink: 0,
            alignSelf: 'stretch',
            borderTopLeftRadius: radius.l,
            borderBottomLeftRadius: radius.l,
          }} />

          {/* Content area with padding and icon */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: '10px', padding: '16px 12px' }}>
            {content?.variant === 'success' ? (
              <CheckCircleIcon sx={{ color: '#31A24C', flexShrink: 0, fontSize: 20 }} />
            ) : (
              <WarningIcon sx={{ color: '#F1A817', flexShrink: 0, fontSize: 20 }} />
            )}
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
              {content?.title && (
                <Typography sx={{ ...typography.styles.body3, color: colors.grey[900], fontWeight: 600 }}>
                  {content.title}
                </Typography>
              )}
              {content?.subtitle && (
                <Typography sx={{ ...typography.styles.body4, color: colors.grey[600] }}>
                  {content.subtitle}
                </Typography>
              )}
            </Box>
          </Box>
        </Box>
      </Snackbar>
    </ForwardingToastContext.Provider>
  );
};


